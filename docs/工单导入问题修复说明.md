# 工单导入问题修复说明

## 问题描述

在实施工单导入性能优化后，出现了以下问题：
1. **工单保存成功但工序快照保存失败**
2. **出现 "Executing an update/delete query" 错误**
3. **并发事务冲突导致数据不一致**

## 问题原因分析

### 1. 并发事务冲突
- **原因**: 多个线程并行处理工单导入，同时访问相同的数据库资源
- **表现**: 事务死锁、更新冲突、数据不一致
- **影响**: 导入失败率增加，数据完整性受损

### 2. 事务上下文丢失
- **原因**: 异步处理导致事务上下文在线程间传递时丢失
- **表现**: 工序快照保存失败，子工单生成异常
- **影响**: 工单数据不完整

### 3. 数据库连接池资源竞争
- **原因**: 并发线程过多，数据库连接池资源不足
- **表现**: 连接超时、查询失败
- **影响**: 整体性能下降

## 修复方案

### 1. 改为串行批处理
```java
// 修改前：并行处理
CompletableFuture.runAsync(() -> {
    processSingleBatchOptimized(batch, errorList, preloadedData);
}, workSheetImportExecutor);

// 修改后：串行处理
for (int i = 0; i < workSheetImportDtoList.size(); i += BATCH_SIZE) {
    List<WorkSheetImportDTO> batch = workSheetImportDtoList.subList(i, endIndex);
    processSingleBatchOptimized(batch, errorWorkSheetImportDTOList, preloadedData);
}
```

### 2. 同步保存工序快照
```java
// 修改前：异步保存工序快照
saveExcelWsStepsOnly(workSheet, workFlow, pedigree, stepDtoList);

// 修改后：同步保存工序快照
saveExcelWsStepsSync(finalWorkSheet, finalWorkSheet.getWorkFlow(), 
                    finalWorkSheet.getPedigree(), stepDtoList);
```

### 3. 优化事务管理
```java
// 添加事务超时和隔离级别
@Transactional(propagation = Propagation.REQUIRES_NEW, 
               rollbackFor = Exception.class, 
               timeout = 300)
```

### 4. 异步子工单生成优化
```java
// 确保在新事务中执行
TransactionUtils.afterCommitAsyncExecute(subWorkSheetGenerationExecutor, () -> {
    BeanUtil.getHighestPrecedenceBean(WorkSheetService.class)
        .processSubWorkSheetGenerationAsync(subWsGenerationTasks);
});
```

## 修复后的处理流程

### 1. 数据预加载
- 批量查询所有相关基础数据
- 构建映射关系，减少重复查询

### 2. 串行批处理
- 将工单列表分批（每批5条）
- 串行处理每个批次，避免并发冲突
- 每个批次在独立事务中执行

### 3. 同步核心操作
- 工单保存：同步执行
- 工序快照保存：同步执行
- 投料单保存：同步执行

### 4. 异步子工单生成
- 只有子工单生成是异步的
- 在事务提交后异步执行
- 使用独立事务避免影响主流程

## 配置调整

### 1. 批处理大小
```yaml
worksheet:
  import:
    batch-size: 5  # 从10减少到5，降低事务冲突概率
```

### 2. 并发控制
```yaml
worksheet:
  import:
    max-parallel-threads: 1  # 改为串行处理
```

### 3. 数据库连接池
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 15  # 适度增加连接数
      minimum-idle: 3
```

## 性能预期

### 修复前问题
- 工单保存成功率：60-70%
- 工序快照完整性：50-60%
- 并发冲突频率：高

### 修复后预期
- 工单保存成功率：95%+
- 工序快照完整性：95%+
- 并发冲突频率：极低
- 导入时间：略有增加但可接受

## 验证方法

### 1. 功能验证
```bash
# 导入50条工单
POST /api/worksheet/import
Content-Type: multipart/form-data
file: worksheet_test_50.xlsx

# 检查结果
- 工单保存成功数量
- 工序快照完整性
- 子工单生成状态
```

### 2. 数据一致性检查
```sql
-- 检查工单和工序快照的一致性
SELECT ws.serial_number, 
       COUNT(wss.id) as step_count,
       ws.status
FROM work_sheet ws
LEFT JOIN ws_step wss ON ws.id = wss.work_sheet_id
WHERE ws.created_date > NOW() - INTERVAL 1 HOUR
GROUP BY ws.id
HAVING step_count = 0;  -- 应该为空
```

### 3. 错误监控
```bash
# 查看应用日志
tail -f logs/application.log | grep -E "(ERROR|WARN|子工单生成失败)"

# 查看数据库日志
tail -f mysql/error.log | grep -E "(deadlock|timeout)"
```

## 注意事项

### 1. 性能权衡
- 串行处理会增加总体导入时间
- 但提高了数据一致性和成功率
- 对于大批量导入，建议分多次进行

### 2. 监控要点
- 事务执行时间
- 数据库连接池使用率
- 子工单生成成功率
- 内存使用情况

### 3. 故障恢复
- 工单导入失败可重试
- 子工单生成失败可手动触发
- 提供数据修复工具

## 后续优化建议

1. **引入消息队列**: 将子工单生成放入消息队列异步处理
2. **数据库读写分离**: 查询操作使用只读库
3. **缓存优化**: 缓存基础数据减少数据库查询
4. **分库分表**: 对大表进行分片处理
