# 工单导入性能优化说明

## 概述

针对50条工单导入超时的问题，我们实施了多维度的性能优化方案，主要包括分批处理、异步子工单生成、数据库优化等措施。

## 优化内容

### 1. 分批并行处理
- **批次大小**: 将大批量工单分为每批10条进行处理
- **并行处理**: 使用线程池并行处理不同批次
- **失败隔离**: 单批失败不影响其他批次的处理

### 2. 异步子工单生成
- **核心优化**: 将最耗时的子工单生成操作改为异步处理
- **事务分离**: 工单保存与子工单生成分离到不同事务
- **状态管理**: 工单先保存成功，子工单后台异步生成

### 3. 数据库优化
- **批量查询**: 预加载所有相关数据（产品谱系、工艺路线等）
- **连接池优化**: 增加数据库连接池大小和优化配置
- **JPA批量处理**: 启用Hibernate批量插入和更新

### 4. 线程池配置
- **工单导入线程池**: 专用于工单导入的并行处理
- **子工单生成线程池**: 专用于异步子工单生成
- **资源隔离**: 不同类型任务使用独立线程池

## 文件变更

### 核心文件
1. `WorkSheetService.java` - 主要优化逻辑
2. `WorkSheetImportOptimizationConfig.java` - 线程池配置
3. `application-worksheet-import-optimization.yml` - 应用配置

### 测试文件
1. `WorkSheetImportPerformanceTest.java` - 性能测试

## 使用方法

### 1. 启用优化配置
在应用启动时添加配置文件：
```bash
--spring.profiles.active=worksheet-import-optimization
```

### 2. 配置参数调整
根据服务器性能调整以下参数：

```yaml
worksheet:
  import:
    batch-size: 10  # 批处理大小，可根据服务器性能调整
    max-parallel-threads: 5  # 最大并行线程数
    timeout-seconds: 300  # 超时时间
```

### 3. 数据库连接池调整
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20  # 根据数据库性能调整
      minimum-idle: 5
```

## 性能预期

### 优化前
- 50条工单导入：超时失败（>5分钟）
- 单条工单平均处理时间：6-10秒

### 优化后
- 50条工单导入：预计2-3分钟完成
- 单条工单平均处理时间：2-3秒
- 支持更大批量导入（100+条）

## 监控指标

### 1. 性能指标
- 导入总时间
- 单条工单处理时间
- 线程池使用率
- 数据库连接池使用率

### 2. 错误监控
- 导入失败率
- 子工单生成失败率
- 超时次数

## 注意事项

### 1. 兼容性
- 保留了原有的导入方法，确保向后兼容
- 新优化方法为默认使用，可通过配置切换

### 2. 数据一致性
- 工单保存和子工单生成分离，但保证最终一致性
- 子工单生成失败不影响工单本身的保存

### 3. 资源使用
- 增加了线程池和数据库连接的使用
- 需要监控服务器资源使用情况

### 4. 故障处理
- 子工单生成失败会记录日志，可后续手动处理
- 提供了重试机制和错误恢复方案

## 测试验证

### 1. 单元测试
运行性能测试：
```bash
mvn test -Dtest=WorkSheetImportPerformanceTest
```

### 2. 集成测试
1. 准备50条测试工单数据
2. 执行导入操作
3. 验证导入时间和成功率
4. 检查子工单生成情况

### 3. 压力测试
1. 测试100条、200条工单导入
2. 并发导入测试
3. 长时间运行稳定性测试

## 故障排查

### 1. 导入超时
- 检查线程池配置
- 检查数据库连接池
- 检查服务器资源使用

### 2. 子工单生成失败
- 查看异步任务日志
- 检查数据库约束
- 验证工艺路线配置

### 3. 内存溢出
- 调整JVM堆内存大小
- 检查批处理大小配置
- 监控对象创建和回收

## 后续优化建议

1. **缓存优化**: 对频繁查询的基础数据进行缓存
2. **数据库分片**: 对大表进行分片处理
3. **消息队列**: 使用消息队列进行异步处理
4. **微服务拆分**: 将工单导入拆分为独立微服务
