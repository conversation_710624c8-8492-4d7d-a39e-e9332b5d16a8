# 子工单生成问题修复说明

## 问题描述

在工单导入优化后，出现了子工单无法生成的问题：

```
Exception in thread "SubWorkSheetGen-1" java.lang.RuntimeException: Can not find highest precedence bean!
at net.airuima.util.BeanUtil.lambda$getHighestPrecedenceBean$2(BeanUtil.java:76)
```

## 问题原因

### 1. Spring上下文传递问题
- **原因**: 异步线程池中无法访问Spring应用上下文
- **表现**: `BeanUtil.getHighestPrecedenceBean()` 方法失败
- **影响**: 子工单生成完全失败

### 2. 事务注解失效
- **原因**: 在异步线程中直接调用带有`@Transactional`注解的方法
- **表现**: 事务管理失效，数据库操作异常
- **影响**: 即使能获取Bean，事务也无法正常工作

## 修复方案

### 1. 创建专门的异步服务

创建 `AsyncSubWorkSheetService` 专门处理异步子工单生成：

```java
@Service
public class AsyncSubWorkSheetService {
    
    @Async("subWorkSheetGenerationExecutor")
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void processSubWorkSheetGenerationAsync(List<WorkSheetSubWsGenerationTask> tasks) {
        // 异步处理逻辑
    }
}
```

### 2. 启用Spring异步支持

在配置类中添加 `@EnableAsync` 注解：

```java
@Configuration
@EnableAsync
public class WorkSheetImportOptimizationConfig {
    // 配置内容
}
```

### 3. 修改调用方式

将原来的手动线程池调用改为Spring的异步方法调用：

```java
// 修改前：手动线程池 + BeanUtil查找
TransactionUtils.afterCommitAsyncExecute(subWorkSheetGenerationExecutor, () -> {
    BeanUtil.getHighestPrecedenceBean(WorkSheetService.class)
        .processSubWorkSheetGenerationAsync(subWsGenerationTasks);
});

// 修改后：Spring异步方法
TransactionUtils.afterCommitSyncExecute(() -> {
    asyncSubWorkSheetService.processSubWorkSheetGenerationAsync(asyncTasks);
});
```

### 4. 任务类型转换

创建独立的任务类，避免类型依赖：

```java
// 在AsyncSubWorkSheetService中定义独立的任务类
public static class WorkSheetSubWsGenerationTask {
    private final Long workSheetId;
    private final List<StepDTO> stepDtoList;
    // ...
}
```

## 修复后的处理流程

### 1. 工单导入阶段
1. 验证和保存工单基础信息
2. 同步保存工序快照
3. 收集子工单生成任务

### 2. 事务提交后
1. 调用 `TransactionUtils.afterCommitSyncExecute()`
2. 转换任务类型
3. 调用异步服务的 `@Async` 方法

### 3. 异步子工单生成
1. Spring管理的异步方法执行
2. 在新事务中处理每个任务
3. 重新查询工单获取最新状态
4. 调用子工单生成服务

## 关键修复点

### 1. Spring上下文管理
- 使用Spring的`@Async`注解而不是手动线程池
- 通过依赖注入获取Bean而不是`BeanUtil`查找
- 确保异步方法在Spring管理的代理中执行

### 2. 事务管理
- 使用`@Transactional(propagation = Propagation.REQUIRES_NEW)`
- 确保每个子工单生成在独立事务中
- 异常处理不影响其他任务

### 3. 线程安全
- 避免在异步线程中访问主线程的变量
- 通过参数传递必要的数据
- 重新查询数据库获取最新状态

## 验证方法

### 1. 功能验证
```bash
# 导入包含子工单的工单
POST /api/worksheet/import
Content-Type: multipart/form-data

# 检查结果
- 工单保存成功
- 工序快照完整
- 子工单正常生成
- 无异常日志
```

### 2. 日志检查
```bash
# 查看应用日志，确保无异常
tail -f logs/application.log | grep -E "(SubWorkSheetGen|子工单生成)"

# 应该看到成功的子工单生成日志，而不是异常
```

### 3. 数据库验证
```sql
-- 检查子工单生成情况
SELECT ws.serial_number, 
       COUNT(sws.id) as sub_worksheet_count,
       ws.generate_sub_ws_status
FROM work_sheet ws
LEFT JOIN sub_work_sheet sws ON ws.id = sws.work_sheet_id
WHERE ws.created_date > NOW() - INTERVAL 1 HOUR
GROUP BY ws.id;
```

## 配置说明

### 1. 异步执行器配置
```yaml
# 子工单生成专用线程池
subWorkSheetGenerationExecutor:
  core-pool-size: 2
  max-pool-size: 5
  queue-capacity: 500
```

### 2. 事务配置
```java
@Transactional(
    propagation = Propagation.REQUIRES_NEW,  // 新事务
    rollbackFor = Exception.class,           // 异常回滚
    timeout = 300                           // 5分钟超时
)
```

## 注意事项

### 1. 性能考虑
- 异步处理不会阻塞主导入流程
- 子工单生成失败不影响工单保存
- 可以通过监控观察异步任务执行情况

### 2. 错误处理
- 每个子工单生成任务独立处理
- 单个任务失败不影响其他任务
- 详细的错误日志便于问题排查

### 3. 监控建议
- 监控异步线程池使用情况
- 监控子工单生成成功率
- 设置告警机制

## 后续优化建议

1. **消息队列**: 考虑使用RabbitMQ或Kafka处理异步任务
2. **重试机制**: 为失败的子工单生成添加重试逻辑
3. **批量处理**: 优化子工单生成的批量操作
4. **监控面板**: 创建子工单生成状态的监控面板
